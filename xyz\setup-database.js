const mysql = require('mysql2/promise');
require('dotenv').config();

async function setupDatabase() {
    console.log('🔧 Setting up XYZ Corp Database...\n');
    
    // First, connect without specifying database to create it
    const connectionConfig = {
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 3306,
        user: process.env.DB_USER || 'root',
        password: process.env.DB_PASSWORD || ''
    };
    
    try {
        console.log('📡 Connecting to MySQL server...');
        const connection = await mysql.createConnection(connectionConfig);
        
        // Create database if it doesn't exist
        const dbName = process.env.DB_NAME || 'xyz_corp_retail';
        console.log(`📊 Creating database: ${dbName}`);
        await connection.execute(`CREATE DATABASE IF NOT EXISTS ${dbName}`);
        console.log('✅ Database created successfully');
        
        // Switch to the database
        await connection.execute(`USE ${dbName}`);
        
        // Create tables
        console.log('📋 Creating tables...');
        
        // Create inventory table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS inventory (
                id INT AUTO_INCREMENT PRIMARY KEY,
                product_name VARCHAR(255) NOT NULL,
                category VARCHAR(100) NOT NULL,
                stock_quantity INT NOT NULL DEFAULT 0,
                unit_price DECIMAL(10, 2) NOT NULL,
                supplier VARCHAR(255),
                last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ Inventory table created');
        
        // Create customers table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS customers (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_name VARCHAR(255) NOT NULL,
                email VARCHAR(255) UNIQUE NOT NULL,
                phone VARCHAR(20),
                address TEXT,
                region VARCHAR(100),
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        `);
        console.log('✅ Customers table created');
        
        // Create sales table
        await connection.execute(`
            CREATE TABLE IF NOT EXISTS sales (
                id INT AUTO_INCREMENT PRIMARY KEY,
                customer_id INT,
                product_id INT,
                quantity_sold INT NOT NULL,
                unit_price DECIMAL(10, 2) NOT NULL,
                total_amount DECIMAL(10, 2) NOT NULL,
                sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                region VARCHAR(100),
                FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
                FOREIGN KEY (product_id) REFERENCES inventory(id) ON DELETE CASCADE
            )
        `);
        console.log('✅ Sales table created');
        
        // Check if we should insert sample data
        const [inventoryRows] = await connection.execute('SELECT COUNT(*) as count FROM inventory');
        const [customerRows] = await connection.execute('SELECT COUNT(*) as count FROM customers');
        
        if (inventoryRows[0].count === 0 && customerRows[0].count === 0) {
            console.log('📦 Inserting sample data...');
            
            // Insert sample inventory
            await connection.execute(`
                INSERT INTO inventory (product_name, category, stock_quantity, unit_price, supplier) VALUES
                ('Laptop Pro 15"', 'Electronics', 25, 1299.99, 'TechSupply Inc'),
                ('Wireless Mouse', 'Electronics', 8, 29.99, 'TechSupply Inc'),
                ('Office Chair', 'Furniture', 15, 199.99, 'FurniCorp'),
                ('Desk Lamp', 'Furniture', 5, 49.99, 'LightCorp'),
                ('Notebook Set', 'Stationery', 50, 12.99, 'PaperPlus'),
                ('Smartphone X1', 'Electronics', 12, 899.99, 'MobileWorld'),
                ('Coffee Maker', 'Appliances', 3, 89.99, 'HomeAppliances Ltd'),
                ('Printer Ink', 'Electronics', 7, 39.99, 'PrintSupply'),
                ('Standing Desk', 'Furniture', 6, 299.99, 'FurniCorp'),
                ('Wireless Headphones', 'Electronics', 20, 149.99, 'AudioTech')
            `);
            
            // Insert sample customers
            await connection.execute(`
                INSERT INTO customers (customer_name, email, phone, address, region) VALUES
                ('John Smith', '<EMAIL>', '******-0101', '123 Main St, New York, NY 10001', 'North'),
                ('Sarah Johnson', '<EMAIL>', '******-0102', '456 Oak Ave, Los Angeles, CA 90210', 'West'),
                ('Michael Brown', '<EMAIL>', '******-0103', '789 Pine Rd, Chicago, IL 60601', 'Central'),
                ('Emily Davis', '<EMAIL>', '******-0104', '321 Elm St, Miami, FL 33101', 'South'),
                ('David Wilson', '<EMAIL>', '******-0105', '654 Maple Dr, Seattle, WA 98101', 'West')
            `);
            
            console.log('✅ Sample data inserted');
        } else {
            console.log('ℹ️  Sample data already exists, skipping insertion');
        }
        
        await connection.end();
        
        console.log('\n🎉 Database setup completed successfully!');
        console.log('📋 Summary:');
        console.log(`   Database: ${dbName}`);
        console.log('   Tables: inventory, customers, sales');
        console.log('   Sample data: Ready for testing');
        console.log('\n🚀 You can now start the application with: npm start');
        
    } catch (error) {
        console.error('❌ Database setup failed:', error.message);
        console.log('\n🔧 Troubleshooting tips:');
        console.log('1. Make sure MySQL server is running');
        console.log('2. Check your database credentials in .env file');
        console.log('3. Ensure the MySQL user has proper permissions');
        console.log('4. Try connecting with a MySQL client first');
        
        if (error.code === 'ER_ACCESS_DENIED_ERROR') {
            console.log('\n🔑 Access denied error - Check your credentials:');
            console.log(`   Host: ${connectionConfig.host}`);
            console.log(`   Port: ${connectionConfig.port}`);
            console.log(`   User: ${connectionConfig.user}`);
            console.log('   Password: [Check .env file]');
        }
        
        process.exit(1);
    }
}

// Run setup if this file is executed directly
if (require.main === module) {
    setupDatabase();
}

module.exports = setupDatabase;
