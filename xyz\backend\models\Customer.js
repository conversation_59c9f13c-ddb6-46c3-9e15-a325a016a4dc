const { pool } = require('../config/db');

class Customer {
    // Get all customers
    static async getAll() {
        try {
            const [rows] = await pool.execute(
                'SELECT * FROM customers ORDER BY customer_name'
            );
            return rows;
        } catch (error) {
            throw new Error(`Error fetching customers: ${error.message}`);
        }
    }

    // Get customer by ID
    static async getById(id) {
        try {
            const [rows] = await pool.execute(
                'SELECT * FROM customers WHERE id = ?',
                [id]
            );
            return rows[0];
        } catch (error) {
            throw new Error(`Error fetching customer: ${error.message}`);
        }
    }

    // Get customer by email
    static async getByEmail(email) {
        try {
            const [rows] = await pool.execute(
                'SELECT * FROM customers WHERE email = ?',
                [email]
            );
            return rows[0];
        } catch (error) {
            throw new Error(`Error fetching customer by email: ${error.message}`);
        }
    }

    // Create new customer
    static async create(customerData) {
        try {
            const { customer_name, email, phone, address, region } = customerData;
            
            // Check if email already exists
            const existingCustomer = await this.getByEmail(email);
            if (existingCustomer) {
                throw new Error('Customer with this email already exists');
            }

            const [result] = await pool.execute(
                `INSERT INTO customers (customer_name, email, phone, address, region) 
                 VALUES (?, ?, ?, ?, ?)`,
                [customer_name, email, phone, address, region]
            );

            return await this.getById(result.insertId);
        } catch (error) {
            throw new Error(`Error creating customer: ${error.message}`);
        }
    }

    // Update customer
    static async update(id, customerData) {
        try {
            const { customer_name, email, phone, address, region } = customerData;
            
            const [result] = await pool.execute(
                `UPDATE customers 
                 SET customer_name = ?, email = ?, phone = ?, address = ?, region = ? 
                 WHERE id = ?`,
                [customer_name, email, phone, address, region, id]
            );

            if (result.affectedRows === 0) {
                throw new Error('Customer not found');
            }

            return await this.getById(id);
        } catch (error) {
            throw new Error(`Error updating customer: ${error.message}`);
        }
    }

    // Delete customer
    static async delete(id) {
        try {
            const [result] = await pool.execute(
                'DELETE FROM customers WHERE id = ?',
                [id]
            );

            if (result.affectedRows === 0) {
                throw new Error('Customer not found');
            }

            return { message: 'Customer deleted successfully' };
        } catch (error) {
            throw new Error(`Error deleting customer: ${error.message}`);
        }
    }
}

module.exports = Customer;
