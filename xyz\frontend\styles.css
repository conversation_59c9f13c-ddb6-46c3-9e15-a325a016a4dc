/* XYZ Corp Data Management System Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

header h1 {
    color: #2c3e50;
    font-size: 2.5em;
    margin-bottom: 10px;
    font-weight: 700;
}

header p {
    color: #7f8c8d;
    font-size: 1.2em;
    font-weight: 500;
}

.tab-navigation {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 10px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.tab-btn {
    background: transparent;
    border: none;
    padding: 15px 25px;
    margin: 0 5px;
    border-radius: 10px;
    cursor: pointer;
    font-size: 1.1em;
    font-weight: 600;
    transition: all 0.3s ease;
    color: #555;
}

.tab-btn:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    transform: translateY(-2px);
}

.tab-btn.active {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.tab-content {
    display: none;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
}

.tab-content.active {
    display: block;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.tab-content h2 {
    color: #2c3e50;
    margin-bottom: 25px;
    font-size: 2em;
    border-bottom: 3px solid #667eea;
    padding-bottom: 10px;
}

.section {
    margin-bottom: 40px;
    padding: 25px;
    background: rgba(248, 249, 250, 0.8);
    border-radius: 12px;
    border-left: 4px solid #667eea;
}

.section h3 {
    color: #34495e;
    margin-bottom: 20px;
    font-size: 1.4em;
    font-weight: 600;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #555;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e8ed;
    border-radius: 8px;
    font-size: 1em;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-1px);
}

button {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

button:active {
    transform: translateY(0);
}

.btn-secondary {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    margin-bottom: 20px;
}

.results {
    margin-top: 20px;
    padding: 20px;
    background: white;
    border-radius: 10px;
    border: 1px solid #e1e8ed;
    max-height: 400px;
    overflow-y: auto;
}

.results:empty {
    display: none;
}

.response-display {
    position: fixed;
    top: 20px;
    right: 20px;
    max-width: 400px;
    padding: 15px 20px;
    border-radius: 10px;
    color: white;
    font-weight: 600;
    z-index: 1000;
    transform: translateX(100%);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
}

.response-display.show {
    transform: translateX(0);
}

.response-display.success {
    background: linear-gradient(135deg, #00b894, #00a085);
}

.response-display.error {
    background: linear-gradient(135deg, #e17055, #d63031);
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 15px;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.data-table th,
.data-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e1e8ed;
}

.data-table th {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.9em;
    letter-spacing: 0.5px;
}

.data-table tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.data-table tr:last-child td {
    border-bottom: none;
}

.summary-card {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 25px;
    border-radius: 12px;
    margin: 15px 0;
    text-align: center;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.summary-card h4 {
    font-size: 1.2em;
    margin-bottom: 10px;
    opacity: 0.9;
}

.summary-card .value {
    font-size: 2.5em;
    font-weight: 700;
    margin-bottom: 5px;
}

.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-btn {
        flex: 1;
        min-width: 120px;
        margin: 5px;
    }
    
    header h1 {
        font-size: 2em;
    }
    
    .tab-content {
        padding: 20px;
    }
    
    .section {
        padding: 15px;
    }
    
    .response-display {
        position: relative;
        top: auto;
        right: auto;
        max-width: none;
        margin: 20px 0;
        transform: none;
    }
    
    .response-display.show {
        transform: none;
    }
}
