{"name": "xyz-corp-jdbc-connectivity", "version": "1.0.0", "description": "XYZ Corp Data Management System with JDBC Connectivity", "main": "backend/index.js", "scripts": {"start": "node backend/index.js", "dev": "nodemon backend/index.js", "setup": "node setup-database.js", "test": "node test-apis.js"}, "keywords": ["jdbc", "mysql", "express", "inventory", "retail"], "author": "XYZ Corp Development Team", "license": "MIT", "dependencies": {"axios": "^1.6.2", "body-parser": "^1.20.2", "cors": "^2.8.5", "dotenv": "^16.6.1", "express": "^4.21.2", "mysql2": "^3.14.2"}, "devDependencies": {"nodemon": "^3.0.2"}}