const { pool } = require('../config/db');

class Sales {
    // Get all sales
    static async getAll() {
        try {
            const [rows] = await pool.execute(`
                SELECT s.*, c.customer_name, c.email, i.product_name, i.category
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                LEFT JOIN inventory i ON s.product_id = i.id
                ORDER BY s.sale_date DESC
            `);
            return rows;
        } catch (error) {
            throw new Error(`Error fetching sales: ${error.message}`);
        }
    }

    // Get sale by ID
    static async getById(id) {
        try {
            const [rows] = await pool.execute(`
                SELECT s.*, c.customer_name, c.email, i.product_name, i.category
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                LEFT JOIN inventory i ON s.product_id = i.id
                WHERE s.id = ?
            `, [id]);
            return rows[0];
        } catch (error) {
            throw new Error(`Error fetching sale: ${error.message}`);
        }
    }

    // Create new sale
    static async create(salesData) {
        const connection = await pool.getConnection();
        
        try {
            await connection.beginTransaction();

            const { customer_id, product_id, quantity_sold, region } = salesData;

            // Get product details and check stock
            const [productRows] = await connection.execute(
                'SELECT * FROM inventory WHERE id = ?',
                [product_id]
            );

            if (productRows.length === 0) {
                throw new Error('Product not found');
            }

            const product = productRows[0];
            
            if (product.stock_quantity < quantity_sold) {
                throw new Error(`Insufficient stock. Available: ${product.stock_quantity}, Requested: ${quantity_sold}`);
            }

            // Calculate total amount
            const unit_price = product.unit_price;
            const total_amount = unit_price * quantity_sold;

            // Insert sale record
            const [saleResult] = await connection.execute(
                `INSERT INTO sales (customer_id, product_id, quantity_sold, unit_price, total_amount, region) 
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [customer_id, product_id, quantity_sold, unit_price, total_amount, region]
            );

            // Update inventory stock
            await connection.execute(
                'UPDATE inventory SET stock_quantity = stock_quantity - ? WHERE id = ?',
                [quantity_sold, product_id]
            );

            await connection.commit();
            
            return await this.getById(saleResult.insertId);
        } catch (error) {
            await connection.rollback();
            throw new Error(`Error creating sale: ${error.message}`);
        } finally {
            connection.release();
        }
    }

    // Get sales by date range
    static async getByDateRange(startDate, endDate) {
        try {
            const [rows] = await pool.execute(`
                SELECT s.*, c.customer_name, c.email, i.product_name, i.category
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                LEFT JOIN inventory i ON s.product_id = i.id
                WHERE s.sale_date BETWEEN ? AND ?
                ORDER BY s.sale_date DESC
            `, [startDate, endDate]);
            return rows;
        } catch (error) {
            throw new Error(`Error fetching sales by date range: ${error.message}`);
        }
    }

    // Get sales summary
    static async getSummary() {
        try {
            const [rows] = await pool.execute(`
                SELECT 
                    COUNT(*) as total_sales,
                    SUM(total_amount) as total_revenue,
                    AVG(total_amount) as average_sale_amount,
                    SUM(quantity_sold) as total_items_sold
                FROM sales
            `);
            return rows[0];
        } catch (error) {
            throw new Error(`Error fetching sales summary: ${error.message}`);
        }
    }
}

module.exports = Sales;
