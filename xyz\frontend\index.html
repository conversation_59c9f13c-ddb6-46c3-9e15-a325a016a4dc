<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XYZ Corp - Data Management System</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>🏢 XYZ Corp Data Management System</h1>
            <p>JDBC Connectivity with MySQL Database</p>
        </header>

        <nav class="tab-navigation">
            <button class="tab-btn active" onclick="showTab('inventory')">📦 Inventory</button>
            <button class="tab-btn" onclick="showTab('sales')">💰 Sales</button>
            <button class="tab-btn" onclick="showTab('customers')">👥 Customers</button>
            <button class="tab-btn" onclick="showTab('reports')">📊 Reports</button>
        </nav>

        <!-- Inventory Tab -->
        <div id="inventory" class="tab-content active">
            <h2>Inventory Management</h2>
            
            <div class="section">
                <h3>Update Product</h3>
                <form id="updateInventoryForm">
                    <div class="form-group">
                        <label for="productId">Product ID:</label>
                        <input type="number" id="productId" required>
                    </div>
                    <div class="form-group">
                        <label for="productName">Product Name:</label>
                        <input type="text" id="productName" required>
                    </div>
                    <div class="form-group">
                        <label for="category">Category:</label>
                        <input type="text" id="category" required>
                    </div>
                    <div class="form-group">
                        <label for="stockQuantity">Stock Quantity:</label>
                        <input type="number" id="stockQuantity" required>
                    </div>
                    <div class="form-group">
                        <label for="unitPrice">Unit Price:</label>
                        <input type="number" id="unitPrice" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="supplier">Supplier:</label>
                        <input type="text" id="supplier">
                    </div>
                    <button type="submit">Update Product</button>
                </form>
            </div>

            <div class="section">
                <h3>Low Stock Alert</h3>
                <button onclick="getLowStock()" class="btn-secondary">Check Low Stock Items</button>
                <div id="lowStockResults" class="results"></div>
            </div>

            <div class="section">
                <h3>All Inventory</h3>
                <button onclick="getAllInventory()" class="btn-secondary">Load All Inventory</button>
                <div id="inventoryResults" class="results"></div>
            </div>
        </div>

        <!-- Sales Tab -->
        <div id="sales" class="tab-content">
            <h2>Sales Management</h2>
            
            <div class="section">
                <h3>Record New Sale</h3>
                <form id="salesForm">
                    <div class="form-group">
                        <label for="customerId">Customer ID:</label>
                        <input type="number" id="customerId" required>
                    </div>
                    <div class="form-group">
                        <label for="saleProductId">Product ID:</label>
                        <input type="number" id="saleProductId" required>
                    </div>
                    <div class="form-group">
                        <label for="quantitySold">Quantity Sold:</label>
                        <input type="number" id="quantitySold" required>
                    </div>
                    <div class="form-group">
                        <label for="region">Region:</label>
                        <input type="text" id="region">
                    </div>
                    <button type="submit">Record Sale</button>
                </form>
            </div>

            <div class="section">
                <h3>Sales History</h3>
                <button onclick="getAllSales()" class="btn-secondary">Load All Sales</button>
                <div id="salesResults" class="results"></div>
            </div>
        </div>

        <!-- Customers Tab -->
        <div id="customers" class="tab-content">
            <h2>Customer Management</h2>
            
            <div class="section">
                <h3>Add New Customer</h3>
                <form id="customerForm">
                    <div class="form-group">
                        <label for="customerName">Customer Name:</label>
                        <input type="text" id="customerName" required>
                    </div>
                    <div class="form-group">
                        <label for="email">Email:</label>
                        <input type="email" id="email" required>
                    </div>
                    <div class="form-group">
                        <label for="phone">Phone:</label>
                        <input type="tel" id="phone">
                    </div>
                    <div class="form-group">
                        <label for="address">Address:</label>
                        <textarea id="address" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="customerRegion">Region:</label>
                        <input type="text" id="customerRegion">
                    </div>
                    <button type="submit">Add Customer</button>
                </form>
            </div>

            <div class="section">
                <h3>All Customers</h3>
                <button onclick="getAllCustomers()" class="btn-secondary">Load All Customers</button>
                <div id="customersResults" class="results"></div>
            </div>
        </div>

        <!-- Reports Tab -->
        <div id="reports" class="tab-content">
            <h2>Reports & Analytics</h2>
            
            <div class="section">
                <h3>Sales Summary</h3>
                <button onclick="getSalesSummary()" class="btn-secondary">Generate Sales Summary</button>
                <div id="summaryResults" class="results"></div>
            </div>

            <div class="section">
                <h3>System Health</h3>
                <button onclick="checkHealth()" class="btn-secondary">Check System Health</button>
                <div id="healthResults" class="results"></div>
            </div>
        </div>

        <!-- Response Display -->
        <div id="responseDisplay" class="response-display"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
