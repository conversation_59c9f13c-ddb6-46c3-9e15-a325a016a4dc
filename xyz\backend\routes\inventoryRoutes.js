const express = require('express');
const router = express.Router();
const InventoryController = require('../controllers/inventoryController');

// Inventory Routes
router.get('/inventory', InventoryController.getAllInventory);
router.get('/low-stock', InventoryController.getLowStock);
router.put('/inventory/:id', InventoryController.updateInventory);

// Customer Routes
router.post('/customer', InventoryController.addCustomer);
router.get('/customers', InventoryController.getAllCustomers);

// Sales Routes
router.post('/sales', InventoryController.insertSale);
router.get('/sales', InventoryController.getAllSales);
router.get('/sales/summary', InventoryController.getSalesSummary);

module.exports = router;
