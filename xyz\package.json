{"name": "xyz-corp-jdbc-connectivity", "version": "1.0.0", "description": "XYZ Corp Data Management System with JDBC Connectivity", "main": "backend/index.js", "scripts": {"start": "node backend/index.js", "dev": "nodemon backend/index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["jdbc", "mysql", "express", "inventory", "retail"], "author": "XYZ Corp Development Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mysql2": "^3.6.5", "dotenv": "^16.3.1", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.2"}}