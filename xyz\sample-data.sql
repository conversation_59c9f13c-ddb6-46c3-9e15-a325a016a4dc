-- Sample Data for XYZ Corp Data Management System
-- Run this script after the application creates the tables

USE xyz_corp_retail;

-- Insert sample inventory data
INSERT INTO inventory (product_name, category, stock_quantity, unit_price, supplier) VALUES
('Laptop Pro 15"', 'Electronics', 25, 1299.99, 'TechSupply Inc'),
('Wireless Mouse', 'Electronics', 8, 29.99, 'TechSupply Inc'),
('Office Chair', 'Furniture', 15, 199.99, 'FurniCorp'),
('Desk Lamp', 'Furniture', 5, 49.99, 'LightCorp'),
('Notebook Set', 'Stationery', 50, 12.99, 'PaperPlus'),
('Smartphone X1', 'Electronics', 12, 899.99, 'MobileWorld'),
('Coffee Maker', 'Appliances', 3, 89.99, 'HomeAppliances Ltd'),
('Printer Ink', 'Electronics', 7, 39.99, 'PrintSupply'),
('Standing Desk', 'Furniture', 6, 299.99, 'FurniCorp'),
('Wireless Headphones', 'Electronics', 20, 149.99, 'AudioTech');

-- Insert sample customer data
INSERT INTO customers (customer_name, email, phone, address, region) VALUES
('<PERSON>', '<EMAIL>', '******-0101', '123 Main St, New York, NY 10001', 'North'),
('<PERSON> Johnson', '<EMAIL>', '******-0102', '456 Oak Ave, Los <PERSON>, CA 90210', 'West'),
('<PERSON> Brown', '<EMAIL>', '******-0103', '789 <PERSON> Rd, Chicago, IL 60601', 'Central'),
('Emily Davis', '<EMAIL>', '******-0104', '321 Elm St, Miami, FL 33101', 'South'),
('David Wilson', '<EMAIL>', '******-0105', '654 Maple Dr, Seattle, WA 98101', 'West'),
('Lisa Anderson', '<EMAIL>', '******-0106', '987 Cedar Ln, Boston, MA 02101', 'North'),
('Robert Taylor', '<EMAIL>', '******-0107', '147 Birch St, Dallas, TX 75201', 'South'),
('Jennifer Martinez', '<EMAIL>', '******-0108', '258 Spruce Ave, Denver, CO 80201', 'Central'),
('William Garcia', '<EMAIL>', '******-0109', '369 Fir Rd, Portland, OR 97201', 'West'),
('Amanda Rodriguez', '<EMAIL>', '******-0110', '741 Ash Dr, Atlanta, GA 30301', 'South');

-- Insert sample sales data
INSERT INTO sales (customer_id, product_id, quantity_sold, unit_price, total_amount, region) VALUES
(1, 1, 2, 1299.99, 2599.98, 'North'),
(2, 2, 3, 29.99, 89.97, 'West'),
(3, 3, 1, 199.99, 199.99, 'Central'),
(4, 4, 2, 49.99, 99.98, 'South'),
(5, 5, 5, 12.99, 64.95, 'West'),
(6, 6, 1, 899.99, 899.99, 'North'),
(7, 7, 1, 89.99, 89.99, 'South'),
(8, 8, 4, 39.99, 159.96, 'Central'),
(9, 9, 1, 299.99, 299.99, 'West'),
(10, 10, 2, 149.99, 299.98, 'South'),
(1, 5, 3, 12.99, 38.97, 'North'),
(2, 2, 2, 29.99, 59.98, 'West'),
(3, 7, 1, 89.99, 89.99, 'Central'),
(4, 10, 1, 149.99, 149.99, 'South'),
(5, 1, 1, 1299.99, 1299.99, 'West');

-- Update inventory stock quantities after sales
UPDATE inventory SET stock_quantity = stock_quantity - 3 WHERE id = 1; -- Laptop Pro 15"
UPDATE inventory SET stock_quantity = stock_quantity - 5 WHERE id = 2; -- Wireless Mouse
UPDATE inventory SET stock_quantity = stock_quantity - 1 WHERE id = 3; -- Office Chair
UPDATE inventory SET stock_quantity = stock_quantity - 2 WHERE id = 4; -- Desk Lamp
UPDATE inventory SET stock_quantity = stock_quantity - 8 WHERE id = 5; -- Notebook Set
UPDATE inventory SET stock_quantity = stock_quantity - 1 WHERE id = 6; -- Smartphone X1
UPDATE inventory SET stock_quantity = stock_quantity - 2 WHERE id = 7; -- Coffee Maker
UPDATE inventory SET stock_quantity = stock_quantity - 4 WHERE id = 8; -- Printer Ink
UPDATE inventory SET stock_quantity = stock_quantity - 1 WHERE id = 9; -- Standing Desk
UPDATE inventory SET stock_quantity = stock_quantity - 3 WHERE id = 10; -- Wireless Headphones

-- Verify low stock items (should show items with stock < 10)
SELECT * FROM inventory WHERE stock_quantity < 10;

-- Verify sales summary
SELECT 
    COUNT(*) as total_sales,
    SUM(total_amount) as total_revenue,
    AVG(total_amount) as average_sale_amount,
    SUM(quantity_sold) as total_items_sold
FROM sales;
