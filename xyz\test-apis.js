const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';

// Test configuration
const testConfig = {
    timeout: 5000,
    validateStatus: function (status) {
        return status >= 200 && status < 500; // Accept 4xx errors for testing
    }
};

async function testAPI(method, endpoint, data = null, description) {
    try {
        console.log(`\n🧪 Testing: ${description}`);
        console.log(`   ${method.toUpperCase()} ${endpoint}`);
        
        let response;
        switch (method.toLowerCase()) {
            case 'get':
                response = await axios.get(`${BASE_URL}${endpoint}`, testConfig);
                break;
            case 'post':
                response = await axios.post(`${BASE_URL}${endpoint}`, data, testConfig);
                break;
            case 'put':
                response = await axios.put(`${BASE_URL}${endpoint}`, data, testConfig);
                break;
            default:
                throw new Error(`Unsupported method: ${method}`);
        }
        
        if (response.status >= 200 && response.status < 300) {
            console.log(`   ✅ Success (${response.status})`);
            if (response.data.data && Array.isArray(response.data.data)) {
                console.log(`   📊 Returned ${response.data.data.length} records`);
            } else if (response.data.message) {
                console.log(`   💬 ${response.data.message}`);
            }
        } else {
            console.log(`   ⚠️  Warning (${response.status}): ${response.data.message || 'Unknown error'}`);
        }
        
        return response;
    } catch (error) {
        if (error.code === 'ECONNREFUSED') {
            console.log(`   ❌ Connection refused - Is the server running?`);
        } else {
            console.log(`   ❌ Error: ${error.message}`);
        }
        return null;
    }
}

async function runTests() {
    console.log('🚀 XYZ Corp JDBC Connectivity - API Testing Suite');
    console.log('=' .repeat(60));
    
    // Test 1: Health Check
    await testAPI('GET', '/health', null, 'System Health Check');
    
    // Test 2: Get All Inventory
    await testAPI('GET', '/inventory', null, 'Get All Inventory Items');
    
    // Test 3: Get Low Stock Items (Required API)
    await testAPI('GET', '/low-stock', null, 'Get Low Stock Items (stock < 10)');
    
    // Test 4: Get All Customers
    await testAPI('GET', '/customers', null, 'Get All Customers');
    
    // Test 5: Add New Customer (Required API)
    const newCustomer = {
        customer_name: 'Test Customer API',
        email: `test.api.${Date.now()}@example.com`,
        phone: '******-TEST',
        address: '123 Test Street, Test City',
        region: 'Test Region'
    };
    const customerResponse = await testAPI('POST', '/customer', newCustomer, 'Add New Customer');
    
    // Test 6: Update Inventory (Required API)
    if (customerResponse && customerResponse.status < 300) {
        const updateData = {
            product_name: 'Updated Test Product',
            category: 'Test Category',
            stock_quantity: 100,
            unit_price: 99.99,
            supplier: 'Test Supplier'
        };
        await testAPI('PUT', '/inventory/1', updateData, 'Update Product (ID: 1)');
    }
    
    // Test 7: Record Sale (Required API)
    const saleData = {
        customer_id: 1,
        product_id: 1,
        quantity_sold: 2,
        region: 'Test Region'
    };
    await testAPI('POST', '/sales', saleData, 'Insert Sales Entry');
    
    // Test 8: Get All Sales
    await testAPI('GET', '/sales', null, 'Get All Sales Records');
    
    // Test 9: Get Sales Summary
    await testAPI('GET', '/sales/summary', null, 'Get Sales Summary');
    
    // Test 10: Test Low Stock Again (should show updated results)
    await testAPI('GET', '/low-stock', null, 'Get Low Stock Items (After Updates)');
    
    console.log('\n' + '=' .repeat(60));
    console.log('🎉 API Testing Complete!');
    console.log('\n📋 Required APIs Tested:');
    console.log('   ✅ POST /api/sales → Insert sales entry');
    console.log('   ✅ GET  /api/low-stock → Products with stock < 10');
    console.log('   ✅ POST /api/customer → Add new customer');
    console.log('   ✅ PUT  /api/inventory/:id → Update product');
    console.log('\n🌐 Open http://localhost:3000 to test via web interface');
}

// Run tests if this file is executed directly
if (require.main === module) {
    runTests().catch(console.error);
}

module.exports = { testAPI, runTests };
