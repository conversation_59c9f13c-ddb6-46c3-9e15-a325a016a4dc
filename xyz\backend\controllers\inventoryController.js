const Inventory = require('../models/Inventory');
const Customer = require('../models/Customer');
const Sales = require('../models/Sales');

class InventoryController {
    // Get all inventory items
    static async getAllInventory(req, res) {
        try {
            const inventory = await Inventory.getAll();
            res.status(200).json({
                success: true,
                message: 'Inventory retrieved successfully',
                data: inventory
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Get low stock items (stock < 10)
    static async getLowStock(req, res) {
        try {
            const lowStockItems = await Inventory.getLowStock();
            res.status(200).json({
                success: true,
                message: 'Low stock items retrieved successfully',
                data: lowStockItems,
                count: lowStockItems.length
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Update inventory item
    static async updateInventory(req, res) {
        try {
            const { id } = req.params;
            const inventoryData = req.body;

            // Validate required fields
            const requiredFields = ['product_name', 'category', 'stock_quantity', 'unit_price'];
            const missingFields = requiredFields.filter(field => !inventoryData[field]);
            
            if (missingFields.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: `Missing required fields: ${missingFields.join(', ')}`
                });
            }

            // Validate data types
            if (isNaN(inventoryData.stock_quantity) || isNaN(inventoryData.unit_price)) {
                return res.status(400).json({
                    success: false,
                    message: 'Stock quantity and unit price must be valid numbers'
                });
            }

            const updatedInventory = await Inventory.update(id, inventoryData);
            res.status(200).json({
                success: true,
                message: 'Inventory updated successfully',
                data: updatedInventory
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Add new customer
    static async addCustomer(req, res) {
        try {
            const customerData = req.body;

            // Validate required fields
            const requiredFields = ['customer_name', 'email'];
            const missingFields = requiredFields.filter(field => !customerData[field]);
            
            if (missingFields.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: `Missing required fields: ${missingFields.join(', ')}`
                });
            }

            // Validate email format
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(customerData.email)) {
                return res.status(400).json({
                    success: false,
                    message: 'Invalid email format'
                });
            }

            const newCustomer = await Customer.create(customerData);
            res.status(201).json({
                success: true,
                message: 'Customer added successfully',
                data: newCustomer
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Insert sales entry
    static async insertSale(req, res) {
        try {
            const salesData = req.body;

            // Validate required fields
            const requiredFields = ['customer_id', 'product_id', 'quantity_sold'];
            const missingFields = requiredFields.filter(field => !salesData[field]);
            
            if (missingFields.length > 0) {
                return res.status(400).json({
                    success: false,
                    message: `Missing required fields: ${missingFields.join(', ')}`
                });
            }

            // Validate data types
            if (isNaN(salesData.customer_id) || isNaN(salesData.product_id) || isNaN(salesData.quantity_sold)) {
                return res.status(400).json({
                    success: false,
                    message: 'Customer ID, Product ID, and Quantity must be valid numbers'
                });
            }

            if (salesData.quantity_sold <= 0) {
                return res.status(400).json({
                    success: false,
                    message: 'Quantity sold must be greater than 0'
                });
            }

            const newSale = await Sales.create(salesData);
            res.status(201).json({
                success: true,
                message: 'Sale recorded successfully',
                data: newSale
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Get all customers
    static async getAllCustomers(req, res) {
        try {
            const customers = await Customer.getAll();
            res.status(200).json({
                success: true,
                message: 'Customers retrieved successfully',
                data: customers
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Get all sales
    static async getAllSales(req, res) {
        try {
            const sales = await Sales.getAll();
            res.status(200).json({
                success: true,
                message: 'Sales retrieved successfully',
                data: sales
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }

    // Get sales summary
    static async getSalesSummary(req, res) {
        try {
            const summary = await Sales.getSummary();
            res.status(200).json({
                success: true,
                message: 'Sales summary retrieved successfully',
                data: summary
            });
        } catch (error) {
            res.status(500).json({
                success: false,
                message: error.message
            });
        }
    }
}

module.exports = InventoryController;
