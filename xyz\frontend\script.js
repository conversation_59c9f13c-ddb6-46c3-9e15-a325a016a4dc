// XYZ Corp Data Management System - Frontend JavaScript

const API_BASE_URL = '/api';

// Tab Management
function showTab(tabName) {
    // Hide all tab contents
    const tabContents = document.querySelectorAll('.tab-content');
    tabContents.forEach(tab => tab.classList.remove('active'));
    
    // Remove active class from all tab buttons
    const tabButtons = document.querySelectorAll('.tab-btn');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    
    // Show selected tab content
    document.getElementById(tabName).classList.add('active');
    
    // Add active class to clicked button
    event.target.classList.add('active');
}

// Utility function to show response messages
function showResponse(message, isSuccess = true) {
    const responseDisplay = document.getElementById('responseDisplay');
    responseDisplay.textContent = message;
    responseDisplay.className = `response-display ${isSuccess ? 'success' : 'error'} show`;
    
    setTimeout(() => {
        responseDisplay.classList.remove('show');
    }, 4000);
}

// Utility function to create data table
function createDataTable(data, containerId) {
    const container = document.getElementById(containerId);
    
    if (!data || data.length === 0) {
        container.innerHTML = '<p>No data available.</p>';
        return;
    }
    
    const table = document.createElement('table');
    table.className = 'data-table';
    
    // Create header
    const thead = document.createElement('thead');
    const headerRow = document.createElement('tr');
    
    Object.keys(data[0]).forEach(key => {
        const th = document.createElement('th');
        th.textContent = key.replace(/_/g, ' ').toUpperCase();
        headerRow.appendChild(th);
    });
    
    thead.appendChild(headerRow);
    table.appendChild(thead);
    
    // Create body
    const tbody = document.createElement('tbody');
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        Object.values(row).forEach(value => {
            const td = document.createElement('td');
            td.textContent = value || 'N/A';
            tr.appendChild(td);
        });
        tbody.appendChild(tr);
    });
    
    table.appendChild(tbody);
    container.innerHTML = '';
    container.appendChild(table);
}

// API Functions

// Update Inventory
async function updateInventory(formData) {
    try {
        const response = await fetch(`${API_BASE_URL}/inventory/${formData.productId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_name: formData.productName,
                category: formData.category,
                stock_quantity: parseInt(formData.stockQuantity),
                unit_price: parseFloat(formData.unitPrice),
                supplier: formData.supplier
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showResponse('Product updated successfully!');
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error updating product: ${error.message}`, false);
        throw error;
    }
}

// Get Low Stock Items
async function getLowStock() {
    try {
        const response = await fetch(`${API_BASE_URL}/low-stock`);
        const result = await response.json();
        
        if (result.success) {
            createDataTable(result.data, 'lowStockResults');
            showResponse(`Found ${result.count} low stock items`);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error fetching low stock items: ${error.message}`, false);
    }
}

// Get All Inventory
async function getAllInventory() {
    try {
        const response = await fetch(`${API_BASE_URL}/inventory`);
        const result = await response.json();
        
        if (result.success) {
            createDataTable(result.data, 'inventoryResults');
            showResponse(`Loaded ${result.data.length} inventory items`);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error fetching inventory: ${error.message}`, false);
    }
}

// Add Customer
async function addCustomer(formData) {
    try {
        const response = await fetch(`${API_BASE_URL}/customer`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                customer_name: formData.customerName,
                email: formData.email,
                phone: formData.phone,
                address: formData.address,
                region: formData.customerRegion
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showResponse('Customer added successfully!');
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error adding customer: ${error.message}`, false);
        throw error;
    }
}

// Get All Customers
async function getAllCustomers() {
    try {
        const response = await fetch(`${API_BASE_URL}/customers`);
        const result = await response.json();
        
        if (result.success) {
            createDataTable(result.data, 'customersResults');
            showResponse(`Loaded ${result.data.length} customers`);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error fetching customers: ${error.message}`, false);
    }
}

// Insert Sale
async function insertSale(formData) {
    try {
        const response = await fetch(`${API_BASE_URL}/sales`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                customer_id: parseInt(formData.customerId),
                product_id: parseInt(formData.saleProductId),
                quantity_sold: parseInt(formData.quantitySold),
                region: formData.region
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showResponse('Sale recorded successfully!');
            return result.data;
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error recording sale: ${error.message}`, false);
        throw error;
    }
}

// Get All Sales
async function getAllSales() {
    try {
        const response = await fetch(`${API_BASE_URL}/sales`);
        const result = await response.json();
        
        if (result.success) {
            createDataTable(result.data, 'salesResults');
            showResponse(`Loaded ${result.data.length} sales records`);
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error fetching sales: ${error.message}`, false);
    }
}

// Get Sales Summary
async function getSalesSummary() {
    try {
        const response = await fetch(`${API_BASE_URL}/sales/summary`);
        const result = await response.json();
        
        if (result.success) {
            const summary = result.data;
            const summaryHTML = `
                <div class="summary-card">
                    <h4>Total Sales</h4>
                    <div class="value">${summary.total_sales || 0}</div>
                </div>
                <div class="summary-card">
                    <h4>Total Revenue</h4>
                    <div class="value">$${parseFloat(summary.total_revenue || 0).toFixed(2)}</div>
                </div>
                <div class="summary-card">
                    <h4>Average Sale Amount</h4>
                    <div class="value">$${parseFloat(summary.average_sale_amount || 0).toFixed(2)}</div>
                </div>
                <div class="summary-card">
                    <h4>Total Items Sold</h4>
                    <div class="value">${summary.total_items_sold || 0}</div>
                </div>
            `;
            document.getElementById('summaryResults').innerHTML = summaryHTML;
            showResponse('Sales summary generated successfully!');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error fetching sales summary: ${error.message}`, false);
    }
}

// Check System Health
async function checkHealth() {
    try {
        const response = await fetch(`${API_BASE_URL}/health`);
        const result = await response.json();
        
        if (result.success) {
            const healthHTML = `
                <div class="summary-card">
                    <h4>System Status</h4>
                    <div class="value">✅ Online</div>
                </div>
                <div style="background: white; padding: 20px; border-radius: 10px; margin-top: 15px;">
                    <h4>System Information</h4>
                    <p><strong>Version:</strong> ${result.version}</p>
                    <p><strong>Timestamp:</strong> ${new Date(result.timestamp).toLocaleString()}</p>
                    <p><strong>Message:</strong> ${result.message}</p>
                </div>
            `;
            document.getElementById('healthResults').innerHTML = healthHTML;
            showResponse('System health check completed!');
        } else {
            throw new Error(result.message);
        }
    } catch (error) {
        showResponse(`Error checking system health: ${error.message}`, false);
    }
}

// Form Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Update Inventory Form
    document.getElementById('updateInventoryForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = {
            productId: document.getElementById('productId').value,
            productName: document.getElementById('productName').value,
            category: document.getElementById('category').value,
            stockQuantity: document.getElementById('stockQuantity').value,
            unitPrice: document.getElementById('unitPrice').value,
            supplier: document.getElementById('supplier').value
        };
        
        try {
            await updateInventory(formData);
            this.reset();
        } catch (error) {
            // Error already handled in updateInventory function
        }
    });
    
    // Customer Form
    document.getElementById('customerForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = {
            customerName: document.getElementById('customerName').value,
            email: document.getElementById('email').value,
            phone: document.getElementById('phone').value,
            address: document.getElementById('address').value,
            customerRegion: document.getElementById('customerRegion').value
        };
        
        try {
            await addCustomer(formData);
            this.reset();
        } catch (error) {
            // Error already handled in addCustomer function
        }
    });
    
    // Sales Form
    document.getElementById('salesForm').addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = {
            customerId: document.getElementById('customerId').value,
            saleProductId: document.getElementById('saleProductId').value,
            quantitySold: document.getElementById('quantitySold').value,
            region: document.getElementById('region').value
        };
        
        try {
            await insertSale(formData);
            this.reset();
        } catch (error) {
            // Error already handled in insertSale function
        }
    });
    
    // Load initial data
    checkHealth();
});
