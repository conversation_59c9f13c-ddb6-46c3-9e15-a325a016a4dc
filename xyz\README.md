# XYZ Corp Data Management System with JDBC Connectivity

## 🏢 Company Background
XYZ Corp is a medium-sized retail company with operations spanning across multiple regions. They manage a vast amount of data related to inventory, sales, and customer information. To streamline their data management processes and enhance decision-making, they have implemented JDBC Connectivity with MySQL databases.

## 🚀 Project Overview
This project implements a comprehensive data management system using:
- **Backend**: Node.js with Express.js framework
- **Database**: MySQL with JDBC-style connectivity using mysql2
- **Frontend**: HTML, CSS, and JavaScript for API testing
- **Architecture**: RESTful API with MVC pattern

## 📁 Project Structure
```
xyz-corp-jdbc-connectivity/
├── frontend/
│   ├── index.html          # Main UI for testing APIs
│   ├── styles.css          # Styling for the interface
│   └── script.js           # Frontend JavaScript logic
├── backend/
│   ├── index.js            # Main Express application
│   ├── .env                # Environment variables (DB credentials)
│   ├── config/
│   │   └── db.js           # MySQL connection configuration
│   ├── routes/
│   │   └── inventoryRoutes.js  # API route definitions
│   ├── controllers/
│   │   └── inventoryController.js  # Business logic controllers
│   └── models/
│       ├── Inventory.js    # Inventory data model
│       ├── Customer.js     # Customer data model
│       └── Sales.js        # Sales data model
├── package.json            # Project dependencies
└── README.md              # Project documentation
```

## 🔧 Installation & Setup

### Prerequisites
- Node.js (v14 or higher)
- MySQL Server (v8.0 or higher)
- npm or yarn package manager

### Step 1: Clone and Install Dependencies
```bash
cd xyz
npm install
```

### Step 2: Database Setup
1. Create a MySQL database named `xyz_corp_retail`
2. Update the `.env` file with your database credentials:
```env
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_username
DB_PASSWORD=your_password
DB_NAME=xyz_corp_retail
```

### Step 3: Start the Application
```bash
npm start
# or for development with auto-reload
npm run dev
```

The application will be available at: `http://localhost:3000`

## 📊 API Endpoints

### Core APIs (As Specified)
| Method | Endpoint | Description |
|--------|----------|-------------|
| POST | `/api/sales` | Insert sales entry |
| GET | `/api/low-stock` | Products with stock < 10 |
| POST | `/api/customer` | Add new customer |
| PUT | `/api/inventory/:id` | Update product |

### Additional APIs
| Method | Endpoint | Description |
|--------|----------|-------------|
| GET | `/api/inventory` | Get all inventory items |
| GET | `/api/customers` | Get all customers |
| GET | `/api/sales` | Get all sales records |
| GET | `/api/sales/summary` | Get sales analytics |
| GET | `/api/health` | System health check |

## 🔍 API Usage Examples

### 1. Insert Sales Entry
```bash
POST /api/sales
Content-Type: application/json

{
  "customer_id": 1,
  "product_id": 1,
  "quantity_sold": 5,
  "region": "North"
}
```

### 2. Get Low Stock Items
```bash
GET /api/low-stock
```

### 3. Add New Customer
```bash
POST /api/customer
Content-Type: application/json

{
  "customer_name": "John Doe",
  "email": "<EMAIL>",
  "phone": "+**********",
  "address": "123 Main St, City, State",
  "region": "North"
}
```

### 4. Update Product
```bash
PUT /api/inventory/1
Content-Type: application/json

{
  "product_name": "Updated Product Name",
  "category": "Electronics",
  "stock_quantity": 50,
  "unit_price": 29.99,
  "supplier": "ABC Supplier"
}
```

## 🗄️ Database Schema

### Inventory Table
```sql
CREATE TABLE inventory (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_name VARCHAR(255) NOT NULL,
    category VARCHAR(100) NOT NULL,
    stock_quantity INT NOT NULL DEFAULT 0,
    unit_price DECIMAL(10, 2) NOT NULL,
    supplier VARCHAR(255),
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Customers Table
```sql
CREATE TABLE customers (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    region VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Sales Table
```sql
CREATE TABLE sales (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT,
    product_id INT,
    quantity_sold INT NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_amount DECIMAL(10, 2) NOT NULL,
    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    region VARCHAR(100),
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (product_id) REFERENCES inventory(id) ON DELETE CASCADE
);
```

## 🎯 Key Features

### JDBC-Style Connectivity
- Connection pooling for optimal performance
- Prepared statements for security
- Transaction management for data integrity
- Error handling and connection recovery

### Business Logic
- Automatic stock quantity updates on sales
- Low stock alerts (< 10 items)
- Sales analytics and reporting
- Customer management with email validation

### Frontend Interface
- Responsive web interface for API testing
- Real-time feedback and error handling
- Data visualization with tables
- Form validation and user experience

## 🔒 Security Features
- Environment-based configuration
- SQL injection prevention with prepared statements
- Input validation and sanitization
- Error handling without sensitive data exposure

## 📈 Performance Optimizations
- Database connection pooling
- Efficient SQL queries with proper indexing
- Transaction management for data consistency
- Caching strategies for frequently accessed data

## 🧪 Testing
Use the frontend interface at `http://localhost:3000` to test all API endpoints interactively, or use tools like Postman/curl for API testing.

## 🤝 Contributing
1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## 📄 License
This project is licensed under the MIT License.

## 📞 Support
For support and questions, please contact the XYZ Corp Development Team.
