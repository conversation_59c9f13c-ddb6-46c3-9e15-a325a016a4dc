# 🚀 XYZ Corp JDBC Connectivity - Setup Guide

## Quick Start Instructions

### Step 1: Install Dependencies
```bash
cd xyz
npm install
```

### Step 2: Configure Database Connection

#### Option A: Update .env file (Recommended)
Edit the `backend/.env` file with your MySQL credentials:

```env
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=your_mysql_username
DB_PASSWORD=your_mysql_password
DB_NAME=xyz_corp_retail

# Server Configuration
PORT=3000
NODE_ENV=development
```

#### Option B: Use Default Settings
If you're using MySQL with default settings:
- Username: `root`
- Password: (empty or your root password)
- Host: `localhost`
- Port: `3306`

### Step 3: Setup Database
Run the database setup script:
```bash
node setup-database.js
```

This script will:
- Create the `xyz_corp_retail` database
- Create all required tables (inventory, customers, sales)
- Insert sample data for testing

### Step 4: Start the Application
```bash
npm start
```

The application will be available at: `http://localhost:3000`

## 🔧 Troubleshooting Common Issues

### Issue 1: Access Denied Error
```
❌ Database connection failed: Access denied for user 'root'@'localhost'
```

**Solutions:**
1. **Check MySQL Service**: Ensure MySQL server is running
   ```bash
   # Windows (Command Prompt as Administrator)
   net start mysql
   
   # Or check services.msc for MySQL service
   ```

2. **Verify Credentials**: Test your MySQL connection
   ```bash
   mysql -u root -p
   # Enter your password when prompted
   ```

3. **Reset MySQL Password** (if needed):
   ```bash
   # Stop MySQL service
   net stop mysql
   
   # Start MySQL without password verification
   mysqld --skip-grant-tables
   
   # In another terminal, connect and reset password
   mysql -u root
   USE mysql;
   UPDATE user SET authentication_string=PASSWORD('your_new_password') WHERE User='root';
   FLUSH PRIVILEGES;
   EXIT;
   
   # Restart MySQL normally
   net start mysql
   ```

### Issue 2: Database Connection Timeout
**Solution:** Check if MySQL is running on the correct port:
```bash
netstat -an | findstr 3306
```

### Issue 3: Database Doesn't Exist
**Solution:** The setup script will create the database automatically. If it fails, create manually:
```sql
CREATE DATABASE xyz_corp_retail;
USE xyz_corp_retail;
```

## 🎯 Testing the APIs

### Using the Web Interface
1. Open `http://localhost:3000` in your browser
2. Use the tabs to test different functionalities:
   - **Inventory**: Update products, check low stock
   - **Sales**: Record new sales
   - **Customers**: Add new customers
   - **Reports**: View analytics

### Using curl Commands

#### 1. Check System Health
```bash
curl http://localhost:3000/api/health
```

#### 2. Get Low Stock Items
```bash
curl http://localhost:3000/api/low-stock
```

#### 3. Add New Customer
```bash
curl -X POST http://localhost:3000/api/customer \
  -H "Content-Type: application/json" \
  -d '{
    "customer_name": "Test Customer",
    "email": "<EMAIL>",
    "phone": "+**********",
    "region": "Test Region"
  }'
```

#### 4. Record a Sale
```bash
curl -X POST http://localhost:3000/api/sales \
  -H "Content-Type: application/json" \
  -d '{
    "customer_id": 1,
    "product_id": 1,
    "quantity_sold": 2,
    "region": "North"
  }'
```

#### 5. Update Inventory
```bash
curl -X PUT http://localhost:3000/api/inventory/1 \
  -H "Content-Type: application/json" \
  -d '{
    "product_name": "Updated Laptop",
    "category": "Electronics",
    "stock_quantity": 30,
    "unit_price": 1399.99,
    "supplier": "New Supplier"
  }'
```

## 📊 Sample Data Overview

After running the setup script, you'll have:

### Inventory Items (10 products)
- Electronics: Laptops, Smartphones, Mice, Headphones
- Furniture: Chairs, Desks, Lamps
- Appliances: Coffee Makers
- Stationery: Notebooks

### Customers (5 customers)
- Distributed across different regions (North, South, East, West, Central)
- Complete contact information

### Low Stock Items
Several items will have stock < 10 to test the low stock alert functionality.

## 🔒 Security Notes

1. **Environment Variables**: Never commit `.env` files to version control
2. **Database Credentials**: Use strong passwords in production
3. **Network Security**: Configure MySQL to accept connections only from trusted sources
4. **Input Validation**: The application includes input validation for all API endpoints

## 📞 Need Help?

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify MySQL service is running
3. Test database connection manually
4. Review the `.env` file configuration
5. Check firewall settings if connecting remotely

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ Database setup script completes without errors
- ✅ Application starts and shows "Server is running on http://localhost:3000"
- ✅ Health check endpoint returns success
- ✅ Web interface loads and displays data
- ✅ API endpoints respond correctly
